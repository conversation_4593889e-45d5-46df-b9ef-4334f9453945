from typing import Final
import os
import datetime
import asyncio

# Try to import openai, but make it optional
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI not installed - ChatGPT features will be disabled")

import discord
from discord.ext import commands
from discord import app_commands, Embed
from dotenv import load_dotenv
import requests

from music import Music
from responses import get_response
from btn import InviteButton
from Pinterest_Search import PinterestSearch
from video import Video
from database import db_manager
from logging_config import get_logger, log_command, log_error, log_user_action
from media_sharing import MediaSharing
from events import EventManagement
from reminders import ReminderSystem
from user_info import UserInfo

# Load environment variables
load_dotenv()
TOKEN: Final[str] = os.getenv('DISCORD_TOKEN')
WEATHER_API_KEY: Final[str] = os.getenv('WEATHER_API_KEY')
APPLICATION_ID: Final[str] = os.getenv('DISCORD_APPLICATION_ID')

# Initialize OpenAI client only if available
if OPENAI_AVAILABLE:
    try:
        openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_APIKEY'))
    except Exception as e:
        print(f"⚠️ OpenAI initialization failed: {e}")
        OPENAI_AVAILABLE = False
        openai_client = None
else:
    openai_client = None

UNSPLASH_API_KEY = os.getenv('UnS_APIKEY')
PEXELS_API_KEY = os.getenv('P_APIKEY')


if not TOKEN:
    raise ValueError("DISCORD_TOKEN không được tìm thấy trong môi trường.")
if not WEATHER_API_KEY:
    raise ValueError("WEATHER_API_KEY không được tìm thấy trong môi trường.")

# Intents setup
intents = discord.Intents.default()
intents.message_content = True

# Initialize bot
bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    description="Discord Bot Example",
    application_id=APPLICATION_ID
)

# Commands
@bot.command(name="hello", help="Trả lời câu chào")
async def hello_command(ctx: commands.Context):
    embed = discord.Embed(
        title="👋 Xin chào!",
        description=f"Chào {ctx.author.mention}!",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command(name="bye", help="Trả lời câu tạm biệt")
async def bye_command(ctx: commands.Context):
    embed = discord.Embed(
        title="👋 Tạm biệt!",
        description=f"Tạm biệt {ctx.author.mention}! Hẹn gặp lại!",
        color=0xff0000,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command(name="ping", help="Trả lời ping pong")
async def ping_command(ctx: commands.Context):
    latency = round(bot.latency * 1000)
    embed = discord.Embed(
        title="🏓 Pong!",
        description=f"Độ trễ: {latency}ms",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command(name="roll", help="Lăn xúc xắc")
async def roll_dice_command(ctx: commands.Context):
    import random
    result = random.randint(1, 6)
    embed = discord.Embed(
        title="🎲 Kết quả xúc xắc",
        description=f"Bạn đã lăn được số: **{result}**",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command()
async def invite(ctx: commands.Context):
    inv = await ctx.channel.create_invite()
    embed = discord.Embed(
        title="🔗 Link mời",
        description="Click nút bên dưới để mời người khác vào server!",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed, view=InviteButton(str(inv)))

@bot.command(name="random_image", help="Gửi ảnh ngẫu nhiên từ Pexels")
async def random_image(ctx):
    image_url = get_random_image()
    if image_url:
        embed = discord.Embed(
            title="📷 Ảnh ngẫu nhiên từ Pexels",
            description="Một bức ảnh đẹp cho bạn!",
            color=0x00ff00,
            timestamp=datetime.datetime.utcnow()
        )
        embed.set_image(url=image_url)
        embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
        await ctx.send(embed=embed)
    else:
        error_embed = discord.Embed(
            title="❌ Lỗi",
            description="Không thể lấy ảnh, vui lòng thử lại sau!",
            color=0xff0000
        )
        await ctx.send(embed=error_embed)

@bot.command(name="search_image", help="Tìm kiếm ảnh trên Pexels theo chủ đề")
async def search_image(ctx, *, topic: str):
    images = get_images_by_topic(topic)
    if images:
        # Gửi từng ảnh một cách riêng biệt để hiển thị đúng
        for i, image_url in enumerate(images, 1):
            embed = discord.Embed(
                title=f"🔍 Kết quả {i}/4 cho '{topic}'",
                color=0x00ff00,
                timestamp=datetime.datetime.utcnow()
            )
            embed.set_image(url=image_url)
            embed.set_footer(
                text=f"Ảnh {i}/4 - Yêu cầu bởi {ctx.author.name}",
                icon_url=ctx.author.avatar.url if ctx.author.avatar else None
            )
            await ctx.send(embed=embed)
    else:
        error_embed = discord.Embed(
            title="❌ Không tìm thấy ảnh",
            description=f"Không tìm thấy ảnh nào cho chủ đề '{topic}', vui lòng thử chủ đề khác.",
            color=0xff0000
        )
        await ctx.send(embed=error_embed)

@bot.command(name='remind', help='Nhắc nhở bạn về một nhiệm vụ')
async def remind(ctx, time: int, *, task: str):
    embed = discord.Embed(
        title="⏰ Đã đặt nhắc nhở",
        description=f"Tôi sẽ nhắc nhở bạn về '{task}' sau {time} phút.",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)
    await asyncio.sleep(time * 60)
    reminder_embed = discord.Embed(
        title="🔔 Nhắc nhở!",
        description=f"Đây là nhắc nhở của bạn: **{task}**",
        color=0xff0000,
        timestamp=datetime.datetime.utcnow()
    )
    await ctx.send(embed=reminder_embed)

@bot.command(name='poll', help='Tạo cuộc khảo sát')
async def poll(ctx, question, *options):
    poll_embed = discord.Embed(
        title="📊 Cuộc khảo sát",
        description=question,
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    for i, option in enumerate(options):
        poll_embed.add_field(name=f"{chr(127462 + i)} {option}", value="\u200b", inline=False)
    poll_embed.set_footer(text=f"Tạo bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    message = await ctx.send(embed=poll_embed)
    for i in range(len(options)):
        await message.add_reaction(f"{chr(127462 + i)}")

@bot.command(name='create_channel', help='Tạo kênh mới')
@commands.has_permissions(administrator=True)
async def create_channel(ctx, channel_name: str):
    guild = ctx.guild
    await guild.create_text_channel(channel_name)
    embed = discord.Embed(
        title="✅ Kênh mới đã được tạo",
        description=f"Kênh {channel_name} đã được tạo thành công!",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )
    embed.set_footer(text=f"Tạo bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command(name="birthday", help="Chúc mừng sinh nhật với ảnh chủ đề birthday 🎂")
async def birthday(ctx, *, name: str):
    image_url = get_birthday_image()
    embed = discord.Embed(
        title="🎉 Chúc mừng sinh nhật!",
        description=f"Chúc mừng sinh nhật {name}! Chúc cậu một ngày tốt lành! 🥳",
        color=0xff69b4,
        timestamp=datetime.datetime.utcnow()
    )
    if image_url:
        embed.set_image(url=image_url)
    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

@bot.command(name="command", help="Hiển thị các lệnh của bot")
async def help_command(ctx):
    embed = discord.Embed(
        title="📚 Danh sách lệnh",
        description="Dưới đây là danh sách các lệnh có sẵn:",
        color=0x00ff00,
        timestamp=datetime.datetime.utcnow()
    )

    # Thêm các lệnh prefix
    prefix_commands = "**Các lệnh prefix:**\n"
    for command in bot.commands:
        prefix_commands += f"`!{command.name}` - {command.help}\n"
    embed.add_field(name="Prefix Commands", value=prefix_commands, inline=False)

    # Thêm các lệnh slash
    slash_commands = "**Các lệnh Slash:**\n"
    for command in bot.tree.get_commands():
        slash_commands += f"`/{command.name}` - {command.description}\n"
    embed.add_field(name="Slash Commands", value=slash_commands, inline=False)

    embed.set_footer(text=f"Yêu cầu bởi {ctx.author.name}", icon_url=ctx.author.avatar.url if ctx.author.avatar else None)
    await ctx.send(embed=embed)

# Event: Catch all other messages with prefix
@bot.event
async def on_message(message: discord.Message):
    # Bỏ qua tin nhắn từ bot
    if message.author.bot:
        return

    # Update user activity in database
    try:
        await db_manager.add_or_update_user(
            message.author.id,
            message.author.name,
            message.author.display_name
        )
        await db_manager.update_user_activity(message.author.id)
    except Exception as e:
        print(f"Error updating user activity: {e}")

    # Lấy context để kiểm tra xem tin nhắn có phải là lệnh hợp lệ hay không
    ctx = await bot.get_context(message)

    if not ctx.valid:  # Nếu không phải lệnh hợp lệ
        # Kiểm tra nếu tin nhắn bắt đầu bằng "!"
        if message.content.startswith(bot.command_prefix):
            user_message = message.content.lstrip(bot.command_prefix)  # Loại bỏ tiền tố "!"
            response = get_response(user_message)
            if response:
                await message.channel.send(response)
    else:
        # Nếu là lệnh hợp lệ, để bot tự xử lý
        await bot.process_commands(message)

# Helper function to get weather data
def get_weather_data(city: str):
    url = f"http://api.weatherapi.com/v1/current.json?key={WEATHER_API_KEY}&q={city}&aqi=no"
    respo = requests.get(url)
    if respo.status_code == 200:
        return respo.json()
    return None

# Helper function to get forecast weather data
def get_forecast_data(city: str, days: int = 3):
    url = f"http://api.weatherapi.com/v1/forecast.json?key={WEATHER_API_KEY}&q={city}&days={days}&aqi=no&alerts=no"
    respo = requests.get(url)
    if respo.status_code == 200:
        return respo.json()
    return None

def get_chatgpt_response(prompt: str) -> str:
    """Gọi API OpenAI ChatGPT và trả về phản hồi"""
    if not OPENAI_AVAILABLE or not openai_client:
        return "❌ Tính năng ChatGPT không khả dụng. Vui lòng cài đặt thư viện OpenAI và cấu hình API key."

    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "system", "content": "You are a helpful assistant."},
                      {"role": "user", "content": prompt}]
        )
        return response.choices[0].message.content
    except Exception as e:
        if "insufficient_quota" in str(e):
            return "Bạn đã vượt quá giới hạn sử dụng API. Vui lòng kiểm tra gói dịch vụ và thông tin thanh toán của bạn."
        return f"Lỗi API: {e}"


def get_random_image():
    url = "https://api.pexels.com/v1/curated?per_page=1"
    headers = {"Authorization": PEXELS_API_KEY}
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        if "photos" in data and len(data["photos"]) > 0:
            return data["photos"][0]["src"]["medium"]  # URL của ảnh
    return None  # Trả về None nếu có lỗi

# Function to get 4 images from Pexels based on a search query
def get_images_by_topic(query):
    url = f"https://api.pexels.com/v1/search?query={query}&per_page=4"
    headers = {"Authorization": PEXELS_API_KEY}
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        if "photos" in data and len(data["photos"]) > 0:
            return [photo["src"]["original"] for photo in data["photos"]]  # Lấy danh sách 4 ảnh
    return None  # Trả về None nếu không có ảnh

# Function to get a random birthday image from Pexels
def get_birthday_image():
    url = f"https://api.pexels.com/v1/search?query=birthday&per_page=1"
    headers = {"Authorization": PEXELS_API_KEY}
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        if "photos" in data and len(data["photos"]) > 0:
            return data["photos"][0]["src"]["original"]  # Lấy ảnh đầu tiên
    return None  # Trả về None nếu không có ảnh

# Slash command: Announce
@bot.tree.command(name="announce", description="Gửi thông báo từ bot")
async def announce_command(interaction: discord.Interaction, content: str):
    embed = Embed(
        title="Thông báo từ Bot",
        description="Thông báo discord",
        color=0x00ff00,
        timestamp=datetime.datetime.now(datetime.timezone.utc),
    )
    embed.add_field(name="Content", value=content, inline=False)
    embed.set_footer(text="Contact: .<EMAIL>")
    embed.set_author(
        name="Bui Quang Tung",
        url="https://github.com/IvanScopy/",
        icon_url="https://avatars.githubusercontent.com/u/127161790?s=400&u=8450fdc04eb098797385afea7066c9ed47415770&v=4"
    )
    embed.set_thumbnail(
        url='https://avatars.githubusercontent.com/u/127161790?s=400&u=8450fdc04eb098797385afea7066c9ed47415770&v=4'
    )
    await interaction.response.send_message(embed=embed)  # NOQA


# Slash command: Weather
@bot.tree.command(name="weather", description="Lấy thông tin thời tiết hiện tại của một thành phố")
async def weather_command(interaction: discord.Interaction, city: str):
    weather_data = get_weather_data(city)

    if weather_data:
        current = weather_data["current"]
        condition = current["condition"]
        embed = Embed(
            title=f"🌤️ Thời tiết hiện tại tại {city}",
            description=f"{condition['text']}",
            color=0x00ff00,
            timestamp=datetime.datetime.now(datetime.timezone.utc),
        )

        # Add weather fields
        embed.add_field(name="🌡️ Nhiệt độ", value=f"{current['temp_c']}°C / {current['temp_f']}°F", inline=True)
        embed.add_field(name="🤒 Cảm giác như", value=f"{current['feelslike_c']}°C / {current['feelslike_f']}°F",
                        inline=True)
        embed.add_field(name="💧 Độ ẩm", value=f"{current['humidity']}%", inline=True)
        embed.add_field(name="💨 Tốc độ gió", value=f"{current['wind_kph']} km/h", inline=True)
        embed.add_field(name="📊 Áp suất", value=f"{current['pressure_mb']} hPa", inline=True)
        embed.add_field(name="🌧️ Lượng mưa", value=f"{current['precip_mm']} mm", inline=True)
        embed.add_field(name="☀️ Chỉ số UV", value=f"{current['uv']}", inline=True)
        embed.add_field(name="💎 Điểm sương", value=f"{current['dewpoint_c']}°C", inline=True)
        embed.add_field(name="👁️ Tầm nhìn", value=f"{current['vis_km']} km", inline=True)

        # Add weather icon
        icon_url = f"http:{condition['icon']}"
        embed.set_thumbnail(url=icon_url)

        embed.set_footer(text="Dữ liệu từ WeatherAPI • Sử dụng /forecast để xem dự báo")

        await interaction.response.send_message(embed=embed)  # NOQA
    else:
        await interaction.response.send_message("Không thể lấy dữ liệu thời tiết. Vui lòng thử lại sau!")  # NOQA

# Slash command: Weather Forecast
@bot.tree.command(name="forecast", description="Dự báo thời tiết 3 ngày tới")
async def forecast_command(interaction: discord.Interaction, city: str):
    await interaction.response.defer()

    forecast_data = get_forecast_data(city, 3)

    if forecast_data:
        location = forecast_data["location"]
        forecast_days = forecast_data["forecast"]["forecastday"]

        embed = Embed(
            title=f"📅 Dự báo thời tiết 3 ngày - {location['name']}, {location['country']}",
            color=0x0099ff,
            timestamp=datetime.datetime.now(datetime.timezone.utc),
        )

        for day in forecast_days:
            date = day["date"]
            day_data = day["day"]
            condition = day_data["condition"]

            # Format date
            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d")
            formatted_date = date_obj.strftime("%d/%m (%A)")

            field_value = f"🌡️ **{day_data['mintemp_c']}°C - {day_data['maxtemp_c']}°C**\n"
            field_value += f"🌤️ {condition['text']}\n"
            field_value += f"🌧️ Khả năng mưa: {day_data['daily_chance_of_rain']}%\n"
            field_value += f"💨 Gió: {day_data['maxwind_kph']} km/h\n"
            field_value += f"💧 Độ ẩm: {day_data['avghumidity']}%"

            embed.add_field(
                name=f"📆 {formatted_date}",
                value=field_value,
                inline=False
            )

        embed.set_footer(text="Dữ liệu từ WeatherAPI • Sử dụng /hourly để xem dự báo theo giờ")
        await interaction.followup.send(embed=embed)
    else:
        await interaction.followup.send("Không thể lấy dữ liệu dự báo thời tiết. Vui lòng thử lại sau!")

# Slash command: Hourly Weather
@bot.tree.command(name="hourly", description="Dự báo thời tiết theo giờ trong ngày")
async def hourly_command(interaction: discord.Interaction, city: str):
    await interaction.response.defer()

    forecast_data = get_forecast_data(city, 1)

    if forecast_data:
        location = forecast_data["location"]
        today = forecast_data["forecast"]["forecastday"][0]
        hourly_data = today["hour"]

        embed = Embed(
            title=f"⏰ Dự báo theo giờ hôm nay - {location['name']}",
            color=0xff9500,
            timestamp=datetime.datetime.now(datetime.timezone.utc),
        )

        # Lấy giờ hiện tại
        current_hour = datetime.datetime.now().hour

        # Hiển thị 8 giờ tiếp theo
        hours_to_show = []
        for i in range(8):
            hour_index = (current_hour + i) % 24
            hours_to_show.append(hourly_data[hour_index])

        field_value = ""
        for hour in hours_to_show:
            time = hour["time"].split(" ")[1]  # Lấy phần giờ
            temp = hour["temp_c"]
            condition = hour["condition"]["text"]
            rain_chance = hour["chance_of_rain"]

            field_value += f"🕐 **{time}** - {temp}°C - {condition}\n"
            field_value += f"   🌧️ Mưa: {rain_chance}% | 💨 Gió: {hour['wind_kph']} km/h\n\n"

        embed.add_field(
            name="📊 Dự báo 8 giờ tiếp theo",
            value=field_value,
            inline=False
        )

        embed.set_footer(text="Dữ liệu từ WeatherAPI")
        await interaction.followup.send(embed=embed)
    else:
        await interaction.followup.send("Không thể lấy dữ liệu dự báo theo giờ. Vui lòng thử lại sau!")

# slash command chat
@bot.tree.command(name="chatgpt", description="Gửi prompt đến ChatGPT và nhận phản hồi")
async def chatgpt_command(interaction: discord.Interaction, prompt: str):
    await interaction.response.defer()  # Tránh bị timeout nếu API chậm
    response = get_chatgpt_response(prompt)
    await interaction.followup.send(f"**ChatGPT trả lời:**\n{response}")

@bot.tree.command(name="ask", description="Hỏi ChatGPT một câu hỏi")
async def ask_command(interaction: discord.Interaction, question: str):
    await interaction.response.defer()
    response = get_chatgpt_response(question)
    await interaction.followup.send(f"**ChatGPT trả lời:**\n{response}")



# Sự kiện: Bot sẵn sàng
@bot.event
async def on_ready():
    print(f"{bot.user} đã sẵn sàng hoạt động!")
    try:
        await bot.tree.sync()
        print("Đã đồng bộ slash commands.")
    except Exception as e:
        print(f"Lỗi khi đồng bộ slash commands: {e}")

@bot.event
async def on_member_join(member):
    # Add user to database
    try:
        await db_manager.add_or_update_user(
            member.id,
            member.name,
            member.display_name
        )
        log_user_action("member_join", member.id, member.guild.id, f"Joined server: {member.guild.name}")
    except Exception as e:
        print(f"Error adding user to database: {e}")

    # Send welcome message
    channel = discord.utils.get(member.guild.text_channels, name='general')
    if channel:
        embed = discord.Embed(
            title="🎉 Chào mừng thành viên mới!",
            description=f"Chào mừng {member.mention} đến với **{member.guild.name}**!",
            color=0x00ff00,
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        embed.set_thumbnail(url=member.display_avatar.url)
        embed.add_field(
            name="👤 Thành viên thứ",
            value=str(member.guild.member_count),
            inline=True
        )
        await channel.send(embed=embed)

@bot.event
async def on_member_remove(member):
    try:
        log_user_action("member_leave", member.id, member.guild.id, f"Left server: {member.guild.name}")
    except Exception as e:
        print(f"Error logging member leave: {e}")

    channel = discord.utils.get(member.guild.text_channels, name='general')
    if channel:
        embed = discord.Embed(
            title="👋 Thành viên đã rời",
            description=f"{member.display_name} đã rời khỏi server.",
            color=0xff6b6b,
            timestamp=datetime.datetime.now(datetime.timezone.utc)
        )
        await channel.send(embed=embed)


# Main coroutine
async def main():
    try:
        # Initialize database
        await db_manager.init_database()
        print("✅ Database initialized successfully")

        async with bot:
            # Add all cogs
            await bot.add_cog(PinterestSearch(bot))
            await bot.add_cog(Music(bot))
            await bot.add_cog(Video(bot))
            await bot.add_cog(MediaSharing(bot))
            await bot.add_cog(EventManagement(bot))
            await bot.add_cog(ReminderSystem(bot))
            await bot.add_cog(UserInfo(bot))

            print("✅ All cogs loaded successfully")
            await bot.start(TOKEN)
    except KeyboardInterrupt:
        print("\nBot đang dừng...")
    finally:
        if not bot.is_closed():
            await bot.close()
        print("Bot đã dừng hoạt động.")


if __name__ == "__main__":
    import asyncio
    try:
        # Chạy bot
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nĐã nhận lệnh dừng chương trình...")
    finally:
        print("Chương trình đã dừng hoàn toàn.")
