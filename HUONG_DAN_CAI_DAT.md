# 🚀 Hướng Dẫn Cài Đặt Bot Discord Nâng Cao

Hướng dẫn chi tiết từng bước để cài đặt và chạy bot Discord với đầy đủ tính năng.

## 📋 Yêu C<PERSON>u Hệ Thống

### Ph<PERSON>n Mềm Cần Thiết
- **Python 3.8+**: [Tải tại đây](https://www.python.org/downloads/)
- **Git**: [Tải tại đây](https://git-scm.com/downloads)
- **FFmpeg**: [Tải tại đây](https://ffmpeg.org/download.html) (cho chức năng nghe nhạc)

### API Keys Cần Thiết
1. **Discord Bot Token** - Từ Discord Developer Portal
2. **WeatherAPI Key** - Cho lệnh thời tiết
3. **OpenAI API Key** - Cho ChatGPT (tùy chọn)
4. **Pexels API Key** - Cho tìm kiếm ảnh
5. **Unsplash API Key** - <PERSON> ảnh bổ sung (tù<PERSON> chọn)

## 🔧 Bước 1: Tạo Discord Bot

### 1.1 Truy cập Discord Developer Portal
1. Đi tới [Discord Developer Portal](https://discord.com/developers/applications)
2. Đăng nhập với tài khoản Discord của bạn
3. Nhấp "New Application"
4. Đặt tên cho bot và nhấp "Create"

### 1.2 Tạo Bot
1. Trong sidebar, nhấp "Bot"
2. Nhấp "Add Bot"
3. Sao chép **Token** (giữ bí mật!)
4. Bật các **Privileged Gateway Intents**:
   - Presence Intent
   - Server Members Intent
   - Message Content Intent

### 1.3 Mời Bot Vào Server
1. Trong sidebar, nhấp "OAuth2" > "URL Generator"
2. Chọn **Scopes**: `bot` và `applications.commands`
3. Chọn **Bot Permissions**:
   - Send Messages
   - Use Slash Commands
   - Connect
   - Speak
   - Manage Messages
   - Embed Links
   - Attach Files
   - Read Message History
   - Add Reactions
4. Sao chép URL và mở trong trình duyệt để mời bot

## 🔑 Bước 2: Lấy API Keys

### 2.1 WeatherAPI (Miễn phí)
1. Đi tới [WeatherAPI.com](https://www.weatherapi.com/)
2. Đăng ký tài khoản miễn phí
3. Sao chép API key từ dashboard

### 2.2 OpenAI API (Có phí)
1. Đi tới [OpenAI Platform](https://platform.openai.com/)
2. Tạo tài khoản và thêm phương thức thanh toán
3. Tạo API key mới
4. **Lưu ý**: Có phí sử dụng

### 2.3 Pexels API (Miễn phí)
1. Đi tới [Pexels API](https://www.pexels.com/api/)
2. Đăng ký tài khoản miễn phí
3. Sao chép API key

### 2.4 Unsplash API (Miễn phí)
1. Đi tới [Unsplash Developers](https://unsplash.com/developers)
2. Tạo ứng dụng mới
3. Sao chép Access Key

## 💻 Bước 3: Cài Đặt Bot

### 3.1 Tải Source Code
```bash
# Tải repository (thay <repository-url> bằng URL thực)
git clone <repository-url>
cd discord-bot
```

### 3.2 Cài Đặt Thư Viện
```bash
# Chạy script cài đặt tự động
python install_dependencies.py
```

**Hoặc cài đặt thủ công:**
```bash
pip install discord.py>=2.0.0
pip install python-dotenv
pip install openai
pip install requests
pip install yt-dlp
pip install PyNaCl
pip install schedule
pip install aiosqlite
pip install playwright
pip install asyncio-mqtt

# Cài đặt trình duyệt cho Playwright
playwright install
```

### 3.3 Cài Đặt FFmpeg

#### Windows:
1. Tải FFmpeg từ [ffmpeg.org](https://ffmpeg.org/download.html)
2. Giải nén và thêm vào PATH
3. Hoặc sử dụng Chocolatey: `choco install ffmpeg`

#### macOS:
```bash
brew install ffmpeg
```

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install ffmpeg
```

## ⚙️ Bước 4: Cấu Hình

### 4.1 Tạo File .env
Tạo file `.env` trong thư mục gốc:

```env
# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_APPLICATION_ID=your_application_id_here

# API Keys
WEATHER_API_KEY=your_weather_api_key_here
OPENAI_APIKEY=your_openai_api_key_here
P_APIKEY=your_pexels_api_key_here
UnS_APIKEY=your_unsplash_api_key_here
```

### 4.2 Ví Dụ File .env
```env
DISCORD_TOKEN=MTIzNDU2Nzg5MDEyMzQ1Njc4.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx
DISCORD_APPLICATION_ID=1234567890123456789
WEATHER_API_KEY=abcd1234efgh5678ijkl9012mnop3456
OPENAI_APIKEY=sk-1234567890abcdef1234567890abcdef1234567890abcdef
P_APIKEY=1234567890abcdef1234567890abcdef12345678
UnS_APIKEY=abcdef1234567890abcdef1234567890abcdef12
```

## 🚀 Bước 5: Chạy Bot

### 5.1 Chạy Bot
```bash
python Chatbot2/main.py
```

### 5.2 Kiểm Tra Hoạt Động
Nếu thành công, bạn sẽ thấy:
```
✅ Database initialized successfully
✅ All cogs loaded successfully
Bot_Name đã sẵn sàng hoạt động!
Đã đồng bộ slash commands.
```

## 🔍 Bước 6: Kiểm Tra Tính Năng

### 6.1 Lệnh Cơ Bản
- `!hello` - Kiểm tra bot phản hồi
- `!ping` - Kiểm tra độ trễ
- `/userinfo` - Kiểm tra slash command

### 6.2 Tính Năng Nghe Nhạc
1. Vào kênh thoại
2. Gõ `!play tên bài hát`
3. Bot sẽ tham gia và phát nhạc

### 6.3 Tạo Sự Kiện
```
/create_event "Test Event" "2024-01-15 19:00" "Sự kiện thử nghiệm"
```

### 6.4 Tạo Nhắc Nhở
```
/remind "5m" "Test reminder"
```

## ❌ Xử Lý Lỗi Thường Gặp

### Lỗi: "discord module not found"
```bash
pip install discord.py
```

### Lỗi: "FFmpeg not found"
- Cài đặt FFmpeg và thêm vào PATH
- Khởi động lại terminal

### Lỗi: "Invalid token"
- Kiểm tra lại DISCORD_TOKEN trong file .env
- Đảm bảo không có khoảng trắng thừa

### Lỗi: "Missing permissions"
- Kiểm tra quyền hạn bot trong server
- Mời lại bot với đủ quyền

### Lỗi: "Database locked"
- Đóng tất cả instance bot đang chạy
- Xóa file `bot_database.db` và chạy lại

## 📊 Giám Sát Bot

### Xem Logs
```bash
# Xem log chung
tail -f logs/bot.log

# Xem log lỗi
tail -f logs/errors.log
```

### Kiểm Tra Database
Bot tự động tạo file `bot_database.db` để lưu trữ dữ liệu.

## 🔄 Cập Nhật Bot

### Cập Nhật Code
```bash
git pull origin main
python install_dependencies.py
```

### Backup Dữ Liệu
```bash
# Backup database
cp bot_database.db bot_database_backup.db

# Backup logs
cp -r logs logs_backup
```

## 🆘 Hỗ Trợ

### Nếu Gặp Vấn Đề:
1. Kiểm tra logs trong thư mục `logs/`
2. Đảm bảo tất cả API keys đúng
3. Kiểm tra kết nối internet
4. Xem lại quyền hạn bot

### Liên Hệ Hỗ Trợ:
- Tạo issue trên GitHub
- Kiểm tra documentation
- Tham gia Discord server hỗ trợ

## ✅ Checklist Hoàn Thành

- [ ] Cài đặt Python 3.8+
- [ ] Cài đặt Git
- [ ] Cài đặt FFmpeg
- [ ] Tạo Discord bot
- [ ] Lấy tất cả API keys
- [ ] Tải source code
- [ ] Cài đặt thư viện
- [ ] Tạo file .env
- [ ] Chạy bot thành công
- [ ] Kiểm tra các tính năng cơ bản

🎉 **Chúc mừng! Bot Discord của bạn đã sẵn sàng hoạt động!**
