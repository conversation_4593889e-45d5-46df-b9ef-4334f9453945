# Discord Bot Dependencies

# Core Discord library
discord.py>=2.3.0

# YouTube audio extraction
yt-dlp>=2023.7.6

# Audio processing
PyNaCl>=1.5.0

# Database
aiosqlite>=0.19.0

# HTTP requests
aiohttp>=3.8.0
requests>=2.31.0

# Environment variables
python-dotenv>=1.0.0

# AI/ML APIs (optional)
openai>=1.0.0

# Image processing and APIs
Pillow>=10.0.0

# Scheduling
schedule>=1.2.0

# Web automation (optional)
playwright>=1.37.0

# Async utilities
asyncio-mqtt>=0.13.0
asyncio-throttle>=1.0.2

# Logging
colorlog>=6.7.0

# Date/time utilities
python-dateutil>=2.8.2

# Optional: For better performance
uvloop>=0.17.0; sys_platform != "win32"