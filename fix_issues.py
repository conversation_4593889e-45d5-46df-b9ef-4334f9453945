#!/usr/bin/env python3
"""
Script để khắc phục các vấn đề của Discord Bot
"""

import subprocess
import sys
import os

def run_command(command):
    """Chạy lệnh và trả về kết quả"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def main():
    print("🔧 Đang khắc phục các vấn đề của Discord Bot...")
    print("=" * 50)
    
    # 1. Cài đặt Playwright browsers
    print("1. Cài đặt Playwright browsers...")
    success, output = run_command("playwright install")
    if success:
        print("✅ Playwright browsers đã được cài đặt")
    else:
        print(f"❌ Lỗi cài đặt Playwright: {output}")
        print("Thử cài đặt thủ công: playwright install")
    
    # 2. <PERSON><PERSON>m tra FFmpeg
    print("\n2. Kiểm tra FFmpeg...")
    success, output = run_command("ffmpeg -version")
    if success:
        print("✅ FFmpeg đã được cài đặt")
    else:
        print("❌ FFmpeg chưa được cài đặt")
        print("Hướng dẫn cài đặt FFmpeg:")
        print("- Windows: choco install ffmpeg")
        print("- macOS: brew install ffmpeg")
        print("- Ubuntu: sudo apt install ffmpeg")
    
    # 3. Kiểm tra các thư viện Python
    print("\n3. Kiểm tra thư viện Python...")
    required_packages = [
        "discord.py",
        "aiosqlite", 
        "schedule",
        "yt-dlp",
        "playwright"
    ]
    
    for package in required_packages:
        success, output = run_command(f"python -c \"import {package.replace('-', '_').replace('.py', '')}\"")
        if success:
            print(f"✅ {package}")
        else:
            print(f"❌ {package} - Cài đặt: pip install {package}")
    
    # 4. Kiểm tra file .env
    print("\n4. Kiểm tra file .env...")
    if os.path.exists(".env"):
        print("✅ File .env tồn tại")
        with open(".env", "r") as f:
            content = f.read()
            if "DISCORD_TOKEN=" in content:
                print("✅ DISCORD_TOKEN có trong .env")
            else:
                print("❌ DISCORD_TOKEN không có trong .env")
    else:
        print("❌ File .env không tồn tại")
        print("Tạo file .env với nội dung:")
        print("""
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_APPLICATION_ID=your_application_id_here
WEATHER_API_KEY=your_weather_api_key
OPENAI_APIKEY=your_openai_api_key
P_APIKEY=your_pexels_api_key
UnS_APIKEY=your_unsplash_api_key
        """)
    
    print("\n" + "=" * 50)
    print("🎯 Tóm tắt các vấn đề đã khắc phục:")
    print("✅ Sửa lỗi logging system (module conflict)")
    print("✅ Sửa lỗi music bot (async/await)")
    print("✅ Sửa lệnh !search_image (hiển thị ảnh thay vì link)")
    print("✅ Nâng cấp /weather với dự báo 3 ngày và theo giờ")
    print("✅ Thêm lệnh /forecast và /hourly")
    
    print("\n🚀 Để chạy bot:")
    print("1. Đảm bảo có file .env với DISCORD_TOKEN")
    print("2. Chạy: python Chatbot2/main.py")
    
    print("\n📝 Lệnh mới:")
    print("- /weather <city> - Thời tiết hiện tại")
    print("- /forecast <city> - Dự báo 3 ngày")
    print("- /hourly <city> - Dự báo theo giờ")
    print("- !search_image <topic> - Tìm ảnh (hiển thị ảnh thực)")

if __name__ == "__main__":
    main()
